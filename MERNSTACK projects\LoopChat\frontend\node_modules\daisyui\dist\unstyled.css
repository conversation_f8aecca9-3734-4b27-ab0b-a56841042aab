.alert {
    display: grid;
    width: 100%;
    grid-auto-flow: row;
    align-content: flex-start;
    align-items: center;
    justify-items: center;
    gap: 1rem;
    text-align: center
}
@media (min-width: 640px) {
    .alert {
        grid-auto-flow: column;
        grid-template-columns: auto minmax(auto,1fr);
        justify-items: start;
        text-align: start
    }
}
.artboard {
    width: 100%
}
.avatar {
    position: relative;
    display: inline-flex
}
  .avatar > div {
    display: block;
    aspect-ratio: 1 / 1;
    overflow: hidden
}
  .avatar img {
    height: 100%;
    width: 100%;
    object-fit: cover
}
  .avatar.placeholder > div {
    display: flex;
    align-items: center;
    justify-content: center
}
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  height: 1.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  width: fit-content;
  padding-left: 0.563rem;
  padding-right: 0.563rem
}
.btm-nav {
  position: fixed;
  bottom: 0px;
  left: 0px;
  right: 0px;
  display: flex;
  width: 100%;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  padding-bottom: env(safe-area-inset-bottom)
}
  .btm-nav > * {
  position: relative;
  display: flex;
  height: 100%;
  flex-basis: 100%;
  cursor: pointer;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.25rem
}
.breadcrumbs {
    max-width: 100%;
    overflow-x: auto
}
  .breadcrumbs > ul,
  .breadcrumbs > ol {
    display: flex;
    align-items: center;
    white-space: nowrap;
    min-height: min-content
}
  .breadcrumbs > ul > li, .breadcrumbs > ol > li {
    display: flex;
    align-items: center
}
  .breadcrumbs > ul > li > a, .breadcrumbs > ol > li > a {
    display: flex;
    cursor: pointer;
    align-items: center
}
  @media(hover:hover) {
    .breadcrumbs > ul > li > a:hover, .breadcrumbs > ol > li > a:hover {
        text-decoration-line: underline
    }
}
.btn {
  display: inline-flex;
  height: 3rem;
  min-height: 3rem;
  flex-shrink: 0;
  cursor: pointer;
  user-select: none;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  border-radius: var(--rounded-btn, 0.5rem);
  border-color: transparent;
  padding-left: 1rem;
  padding-right: 1rem;
  text-align: center;
  font-size: 0.875rem;
  line-height: 1em
}
  /* disabled */
  .btn-disabled,
  .btn[disabled],
  .btn:disabled {
  pointer-events: none
}
  /* shapes */
  .btn-square {
  height: 3rem;
  width: 3rem;
  padding: 0px
}
  .btn-circle {
  height: 3rem;
  width: 3rem;
  border-radius: 9999px;
  padding: 0px
}

/* radio input and checkbox as button */

:where(.btn:is(input[type="checkbox"])),
:where(.btn:is(input[type="radio"])) {
  width: auto;
  appearance: none
}

.btn:is(input[type="checkbox"]):after,
.btn:is(input[type="radio"]):after {
  --tw-content: attr(aria-label);
  content: var(--tw-content)
}
.card {
      position: relative;
      display: flex;
      flex-direction: column
}
  .card:focus {
      outline: 2px solid transparent;
      outline-offset: 2px
}
  .card-body {
      display: flex;
      flex: 1 1 auto;
      flex-direction: column
}
  .card-body :where(p) {
      flex-grow: 1
}
  .card-actions {
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;
      gap: 0.5rem
}
  .card figure {
      display: flex;
      align-items: center;
      justify-content: center
}
  .card.image-full {
      display: grid
}
  .card.image-full:before {
      position: relative;
      content: ""
}
  .card.image-full:before,
    .card.image-full > * {
      grid-column-start: 1;
      grid-row-start: 1
}
  .card.image-full > figure img {
      height: 100%;
      object-fit: cover
}
  .card.image-full > .card-body {
      position: relative
}
.carousel {
  display: inline-flex;
  overflow-x: scroll;
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
}
  .carousel-vertical {
  flex-direction: column;
  overflow-y: scroll;
    scroll-snap-type: y mandatory;
}
  .carousel-item {
  box-sizing: content-box;
  display: flex;
  flex: none;
    scroll-snap-align: start;
}
  .carousel-start .carousel-item {
    scroll-snap-align: start;
  }
  .carousel-center .carousel-item {
    scroll-snap-align: center;
  }
  .carousel-end .carousel-item {
    scroll-snap-align: end;
  }
.chat {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    column-gap: 0.75rem;
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}
  .chat-image {
    grid-row: span 2 / span 2;
    align-self: flex-end;
}
  .chat-header {
    grid-row-start: 1;
    font-size: 0.875rem;
    line-height: 1.25rem;
}
  .chat-footer {
    grid-row-start: 3;
    font-size: 0.875rem;
    line-height: 1.25rem;
}
  .chat-bubble {
    position: relative;
    display: block;
    width: fit-content;
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    max-width: 90%;
}
  .chat-bubble:before {
    position: absolute;
    bottom: 0px;
    height: 0.75rem;
    width: 0.75rem;
      background-color: inherit;
      content: "";
      mask-size: contain;
      mask-repeat: no-repeat;
      mask-position: center;
}
  .chat-start {
    place-items: start;
    grid-template-columns: auto 1fr;
}
  .chat-start .chat-header {
    grid-column-start: 2;
}
  .chat-start .chat-footer {
    grid-column-start: 2;
}
  .chat-start .chat-image {
    grid-column-start: 1;
}
  .chat-start .chat-bubble {
    grid-column-start: 2;
}
  .chat-start .chat-bubble:before {
        mask-image: url("data:image/svg+xml,%3csvg width='3' height='3' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m 0 3 L 3 3 L 3 0 C 3 1 1 3 0 3'/%3e%3c/svg%3e");
      }
  [dir="rtl"] .chat-start .chat-bubble:before {
          mask-image: url("data:image/svg+xml,%3csvg width='3' height='3' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m 0 3 L 1 3 L 3 3 C 2 3 0 1 0 0'/%3e%3c/svg%3e");
        }
  .chat-end {
    place-items: end;
    grid-template-columns: 1fr auto;
}
  .chat-end .chat-header {
    grid-column-start: 1;
}
  .chat-end .chat-footer {
    grid-column-start: 1;
}
  .chat-end .chat-image {
    grid-column-start: 2;
}
  .chat-end .chat-bubble {
    grid-column-start: 1;
}
  .chat-end .chat-bubble:before {
        mask-image: url("data:image/svg+xml,%3csvg width='3' height='3' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m 0 3 L 1 3 L 3 3 C 2 3 0 1 0 0'/%3e%3c/svg%3e");
      }
  [dir="rtl"] .chat-end .chat-bubble:before {
          mask-image: url("data:image/svg+xml,%3csvg width='3' height='3' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m 0 3 L 3 3 L 3 0 C 3 1 1 3 0 3'/%3e%3c/svg%3e");
        }
.checkbox {
    flex-shrink: 0
}
.collapse:not(td):not(tr):not(colgroup) {
  visibility: visible;
}
.collapse {
  position: relative;
  display: grid;
  overflow: hidden;
  grid-template-rows: max-content 0fr;
  transition: grid-template-rows 0.2s;
}
.collapse-title,
.collapse > input[type="checkbox"],
.collapse > input[type="radio"],
.collapse-content {
  grid-column-start: 1;
  grid-row-start: 1;
}
.collapse > input[type="checkbox"],
.collapse > input[type="radio"] {
  appearance: none;
  opacity: 0;
}
:where(.collapse > input[type="checkbox"]),
:where(.collapse > input[type="radio"]) {
  height: 100%;
  width: 100%;
}
.collapse-content {
  visibility: hidden;
  grid-column-start: 1;
  grid-row-start: 2;
  min-height: 0px;
  transition: visibility 0.2s;
}
.collapse[open],
.collapse-open,
.collapse:focus:not(.collapse-close) {
  grid-template-rows: max-content 1fr;
}
.collapse:not(.collapse-close):has(> input[type="checkbox"]:checked),
.collapse:not(.collapse-close):has(> input[type="radio"]:checked) {
  grid-template-rows: max-content 1fr;
}
.collapse[open] > .collapse-content,
.collapse-open > .collapse-content,
.collapse:focus:not(.collapse-close) > .collapse-content,
.collapse:not(.collapse-close) > input[type="checkbox"]:checked ~ .collapse-content,
.collapse:not(.collapse-close) > input[type="radio"]:checked ~ .collapse-content {
  visibility: visible;
  min-height: fit-content;
}
:root .countdown {
  line-height: 1em;
}
.countdown {
  display: inline-flex;
}
.countdown > * {
    height: 1em;
    display: inline-block;
    overflow-y: hidden;
  }
.countdown > *:before {
      position: relative;
      content: "00\A 01\A 02\A 03\A 04\A 05\A 06\A 07\A 08\A 09\A 10\A 11\A 12\A 13\A 14\A 15\A 16\A 17\A 18\A 19\A 20\A 21\A 22\A 23\A 24\A 25\A 26\A 27\A 28\A 29\A 30\A 31\A 32\A 33\A 34\A 35\A 36\A 37\A 38\A 39\A 40\A 41\A 42\A 43\A 44\A 45\A 46\A 47\A 48\A 49\A 50\A 51\A 52\A 53\A 54\A 55\A 56\A 57\A 58\A 59\A 60\A 61\A 62\A 63\A 64\A 65\A 66\A 67\A 68\A 69\A 70\A 71\A 72\A 73\A 74\A 75\A 76\A 77\A 78\A 79\A 80\A 81\A 82\A 83\A 84\A 85\A 86\A 87\A 88\A 89\A 90\A 91\A 92\A 93\A 94\A 95\A 96\A 97\A 98\A 99\A";
      white-space: pre;
      top: calc(var(--value) * -1em);
    }
.diff {
  position: relative;
  display: grid;
  width: 100%;
  overflow: hidden;
  direction: ltr;
  container-type: inline-size;
  grid-template-columns: auto 1fr
}
.diff-resizer {
  position: relative;
  top: 50%;
  z-index: 1;
  height: 3rem;
  width: 25rem;
  min-width: 1rem;
  max-width: calc(100cqi - 1rem);
  resize: horizontal;
  overflow: hidden;
  opacity: 0;
  transform-origin: 100% 100%;
  scale: 4;
  translate: 1.5rem -1.5rem;
  clip-path: inset(calc(100% - 0.75rem) 0 0 calc(100% - 0.75rem))
}
.diff-resizer,
.diff-item-1,
.diff-item-2 {
  position: relative;
  grid-column-start: 1;
  grid-row-start: 1
}
.diff-item-1:after {
  pointer-events: none;
  position: absolute;
  bottom: 0px;
  right: 1px;
  top: 50%;
  z-index: 1;
  height: 2rem;
  width: 2rem;
  --tw-content: '';
  content: var(--tw-content);
  translate: 50% -50%
}
.diff-item-2 {
  overflow: hidden
}
.diff-item-1 > *,
.diff-item-2 > * {
  pointer-events: none;
  position: absolute;
  bottom: 0px;
  left: 0px;
  top: 0px;
  height: 100%;
  width: 100cqi;
  max-width: none;
  object-fit: cover;
  object-position: center
}
.divider {
    display: flex;
    flex-direction: row;
    align-items: center;
    align-self: stretch
}
  .divider:before,
  .divider:after {
    height: 0.125rem;
    width: 100%;
    flex-grow: 1;
    --tw-content: '';
    content: var(--tw-content)
}
  .divider-start:before {
    display: none
}
  .divider-end:after {
    display: none
}
.drawer {
  position: relative;
  display: grid;
  grid-auto-columns: max-content auto;
}
  .drawer-content {
  grid-column-start: 2;
  grid-row-start: 1;
  min-width: 0px;
}
  .drawer-side {
  pointer-events: none;
  position: fixed;
  inset-inline-start: 0px;
  top: 0px;
  grid-column-start: 1;
  grid-row-start: 1;
  display: grid;
  width: 100%;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  grid-template-rows: repeat(1, minmax(0, 1fr));
  align-items: flex-start;
  justify-items: start;
  overflow-x: hidden;
  overflow-y: hidden;
  overscroll-behavior: contain;
    height: 100vh;
    height: 100dvh;
}
  .drawer-side > .drawer-overlay {
  position: sticky;
  top: 0px;
  place-self: stretch;
}
  .drawer-side > * {
  grid-column-start: 1;
  grid-row-start: 1;
}
  .drawer-side > *:not(.drawer-overlay) {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  will-change: transform;
      transform: translateX(-100%);
}
  [dir="rtl"] .drawer-side > *:not(.drawer-overlay) {
        transform: translateX(100%);
      }
  .drawer-toggle {
  position: fixed;
  height: 0px;
  width: 0px;
  appearance: none;
  opacity: 0;
}
  .drawer-toggle:checked ~ .drawer-side {
  pointer-events: auto;
  visibility: visible;
  overflow-y: auto;
}
  .drawer-toggle:checked ~ .drawer-side > *:not(.drawer-overlay) {
          transform: translateX(0%);
        }
  .drawer-end {
    grid-auto-columns: auto max-content;
  }
  .drawer-end > .drawer-toggle ~ .drawer-content {
  grid-column-start: 1;
}
  .drawer-end > .drawer-toggle ~ .drawer-side {
  grid-column-start: 2;
  justify-items: end;
}
  .drawer-end > .drawer-toggle ~ .drawer-side > *:not(.drawer-overlay) {
        transform: translateX(100%);
      }
  [dir="rtl"] .drawer-end > .drawer-toggle ~ .drawer-side > *:not(.drawer-overlay) {
          transform: translateX(-100%);
        }
  .drawer-end > .drawer-toggle:checked ~ .drawer-side > *:not(.drawer-overlay) {
        transform: translateX(0%);
      }
.dropdown {
  position: relative;
  display: inline-block
}
.dropdown > *:not(summary):focus {
  outline: 2px solid transparent;
  outline-offset: 2px
}
.dropdown .dropdown-content {
  position: absolute
}
.dropdown:is(:not(details)) .dropdown-content {
  visibility: hidden;
  opacity: 0
}
.dropdown-end .dropdown-content {
  inset-inline-end: 0px
}
.dropdown-left .dropdown-content {
  bottom: auto;
  inset-inline-end: 100%;
  top: 0px
}
.dropdown-right .dropdown-content {
  bottom: auto;
  inset-inline-start: 100%;
  top: 0px
}
.dropdown-bottom .dropdown-content {
  bottom: auto;
  top: 100%
}
.dropdown-top .dropdown-content {
  bottom: 100%;
  top: auto
}
.dropdown-end.dropdown-right .dropdown-content {
  bottom: 0px;
  top: auto
}
.dropdown-end.dropdown-left .dropdown-content {
  bottom: 0px;
  top: auto
}
.dropdown.dropdown-open .dropdown-content,
.dropdown:not(.dropdown-hover):focus .dropdown-content,
.dropdown:focus-within .dropdown-content {
  visibility: visible;
  opacity: 1
}
@media (hover: hover) {
  .dropdown.dropdown-hover:hover .dropdown-content {
    visibility: visible;
    opacity: 1
  }
}
.dropdown:is(details) summary::-webkit-details-marker {
  display: none
}
.file-input {
    height: 3rem;
    flex-shrink: 1;
    padding-inline-end: 1rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    line-height: 2
}
  .file-input::file-selector-button {
    margin-inline-end: 1rem;
    display: inline-flex;
    height: 100%;
    flex-shrink: 0;
    cursor: pointer;
    user-select: none;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    padding-left: 1rem;
    padding-right: 1rem;
    text-align: center;
    font-size: 0.875rem;
    line-height: 1.25rem;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
    line-height: 1em
}
.footer {
  display: grid;
  width: 100%;
  grid-auto-flow: row;
  place-items: start;
}
  .footer > * {
  display: grid;
  place-items: start;
}
  .footer-center {
  place-items: center;
  text-align: center;
}
  .footer-center > * {
  place-items: center;
}
@media (min-width: 48rem) {
  .footer {
    grid-auto-flow: column;
  }
  .footer-center {
    grid-auto-flow: row dense;
  }
}
.form-control {
    display: flex;
    flex-direction: column
}
.label {
    display: flex;
    user-select: none;
    align-items: center;
    justify-content: space-between
}
.hero {
    display: grid;
    width: 100%;
    place-items: center;
    background-size: cover;
    background-position: center
}
  .hero > * {
    grid-column-start: 1;
    grid-row-start: 1
}
  .hero-overlay {
    grid-column-start: 1;
    grid-row-start: 1;
    height: 100%;
    width: 100%
}
  .hero-content {
    z-index: 0;
    display: flex;
    align-items: center;
    justify-content: center
}
.indicator {
  position: relative;
  display: inline-flex;
  width: max-content;
}
  .indicator :where(.indicator-item) {
    z-index: 1;
    position: absolute;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
    white-space: nowrap;
  }
.input {
    flex-shrink: 1;
    appearance: none;
    height: 3rem;
    padding-left: 1rem;
    padding-right: 1rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    line-height: 2
}
.input[type="number"]::-webkit-inner-spin-button,
.input-md[type="number"]::-webkit-inner-spin-button {
    margin-top: -1rem;
    margin-bottom: -1rem;
    margin-inline-end: -1rem
}
.input-xs[type="number"]::-webkit-inner-spin-button {
    margin-top: -0.25rem;
    margin-bottom: -0.25rem;
    margin-inline-end: -0px
}
.input-sm[type="number"]::-webkit-inner-spin-button {
    margin-top: 0px;
    margin-bottom: 0px;
    margin-inline-end: -0px
}
.input-lg[type="number"]::-webkit-inner-spin-button {
    margin-top: -1.5rem;
    margin-bottom: -1.5rem;
    margin-inline-end: -1.5rem
}
.join {
    display: inline-flex;
    align-items: stretch;
}
  .join :where(.join-item) {
    border-start-end-radius: 0;
    border-end-end-radius: 0;
    border-end-start-radius: 0;
    border-start-start-radius: 0;
  }
  .join .join-item:not(:first-child):not(:last-child),
  .join *:not(:first-child):not(:last-child) .join-item {
    border-start-end-radius: 0;
    border-end-end-radius: 0;
    border-end-start-radius: 0;
    border-start-start-radius: 0;
  }
  .join .join-item:first-child:not(:last-child),
  .join *:first-child:not(:last-child) .join-item {
    border-start-end-radius: 0;
    border-end-end-radius: 0;
  }
  .join .dropdown .join-item:first-child:not(:last-child),
  .join *:first-child:not(:last-child) .dropdown .join-item {
    border-start-end-radius: inherit;
    border-end-end-radius: inherit;
  }
  .join :where(.join-item:first-child:not(:last-child)),
  .join :where(*:first-child:not(:last-child) .join-item) {
    border-end-start-radius: inherit;
    border-start-start-radius: inherit;
  }
  .join .join-item:last-child:not(:first-child),
  .join *:last-child:not(:first-child) .join-item {
    border-end-start-radius: 0;
    border-start-start-radius: 0;
  }
  .join :where(.join-item:last-child:not(:first-child)),
  .join :where(*:last-child:not(:first-child) .join-item) {
    border-start-end-radius: inherit;
    border-end-end-radius: inherit;
  }

@supports not selector(:has(*)) {
  :where(.join *) {
        border-radius: inherit;
    }
}

@supports selector(:has(*)) {
  :where(.join *:has(.join-item)) {
        border-radius: inherit;
    }
}
.kbd {
    display: inline-flex;
    align-items: center;
    justify-content: center
}
.link {
    cursor: pointer;
    text-decoration-line: underline
}
  .link-hover {
    text-decoration-line: none
}
  @media(hover:hover) {
    .link-hover:hover {
        text-decoration-line: underline
    }
}
.mask {
  mask-size: contain;
  mask-repeat: no-repeat;
  mask-position: center;
}
.mask-half-1 {
  mask-size: 200%;
  mask-position: left;
}
.mask-half-1:where([dir="rtl"], [dir="rtl"] *) {
  mask-position: right;
}
.mask-half-2 {
  mask-size: 200%;
  mask-position: right;
}
.mask-half-2:where([dir="rtl"], [dir="rtl"] *) {
  mask-position: left;
}
.menu {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    font-size: 0.875rem;
    line-height: 1.25rem
}
  .menu :where(li ul) {
    position: relative;
    white-space: nowrap
}
  .menu :where(li:not(.menu-title) > *:not(ul, details, .menu-title, .btn)), .menu :where(li:not(.menu-title) > details > summary:not(.menu-title)) {
    display: grid;
    grid-auto-flow: column;
    align-content: flex-start;
    align-items: center;
    gap: 0.5rem;
    grid-auto-columns: minmax(auto, max-content) auto max-content;
    user-select: none
}
  .menu li.disabled {
    cursor: not-allowed;
    user-select: none
}
  .menu :where(li > .menu-dropdown:not(.menu-dropdown-show)) {
    display: none
}
:where(.menu li) {
    position: relative;
    display: flex;
    flex-shrink: 0;
    flex-direction: column;
    flex-wrap: wrap;
    align-items: stretch
}
:where(.menu li) .badge {
    justify-self: end
}
.mockup-code {
        position: relative;
        overflow: hidden;
        overflow-x: auto;
}
    .mockup-code pre[data-prefix]:before {
        content: attr(data-prefix);
        display: inline-block;
        text-align: right;
      }
  .mockup-window {
        position: relative;
        overflow: hidden;
        overflow-x: auto;
}
  .mockup-window pre[data-prefix]:before {
        content: attr(data-prefix);
        display: inline-block;
        text-align: right;
      }
  .mockup-browser {
        position: relative;
        overflow: hidden;
        overflow-x: auto;
}
  .mockup-browser pre[data-prefix]:before {
        content: attr(data-prefix);
        display: inline-block;
        text-align: right;
      }
.modal {
  /* @apply pointer-events-none invisible fixed inset-0 flex justify-center opacity-0; */
  pointer-events: none;
  position: fixed;
  inset: 0px;
  margin: 0px;
  display: grid;
  height: 100%;
  max-height: none;
  width: 100%;
  max-width: none;
  justify-items: center;
  padding: 0px;
  opacity: 0;
  overscroll-behavior: contain;
  z-index: 999;
}
.modal-scroll {
  overscroll-behavior: auto;
}
:where(.modal) {
  align-items: center;
}
.modal-box {
  max-height: calc(100vh - 5em);
}
.modal-open,
.modal:target,
.modal-toggle:checked + .modal,
.modal[open] {
  pointer-events: auto;
  visibility: visible;
  opacity: 1;
}
.modal-action {
  display: flex;
}
.modal-toggle {
  position: fixed;
  height: 0px;
  width: 0px;
  appearance: none;
  opacity: 0;
}
:root:has(:is(.modal-open, .modal:target, .modal-toggle:checked + .modal, .modal[open])) {
  overflow: hidden;
  scrollbar-gutter: stable;
}
.navbar {
  display: flex;
  align-items: center;
}
:where(.navbar > *:not(script, style)) {
  display: inline-flex;
  align-items: center;
}
.navbar-start {
  width: 50%;
  justify-content: flex-start;
}
.navbar-center {
  flex-shrink: 0;
}
.navbar-end {
  width: 50%;
  justify-content: flex-end;
}
.progress {
    position: relative;
    width: 100%;
    appearance: none;
    overflow: hidden
}
.radial-progress {
  position: relative;
  display: inline-grid;
  height: var(--size);
  width: var(--size);
  place-content: center;
  border-radius: 9999px;
  background-color: transparent;
  vertical-align: middle;
  box-sizing: content-box;
}
.radial-progress::-moz-progress-bar {
  appearance: none;
  background-color: transparent;
}
.radial-progress::-webkit-progress-value {
  appearance: none;
  background-color: transparent;
}
.radial-progress::-webkit-progress-bar {
  appearance: none;
  background-color: transparent;
}
.radial-progress:before,
.radial-progress:after {
  position: absolute;
  border-radius: 9999px;
  content: "";
}
.radial-progress:before {
  inset: 0px;
  background:
    radial-gradient(farthest-side, currentColor 98%, #0000) top/var(--thickness) var(--thickness)
      no-repeat,
    conic-gradient(currentColor calc(var(--value) * 1%), #0000 0);
  -webkit-mask: radial-gradient(
    farthest-side,
    #0000 calc(99% - var(--thickness)),
    #000 calc(100% - var(--thickness))
  );
  mask: radial-gradient(
    farthest-side,
    #0000 calc(99% - var(--thickness)),
    #000 calc(100% - var(--thickness))
  );
}
.radial-progress:after {
  inset: calc(50% - var(--thickness) / 2);
  transform: rotate(calc(var(--value) * 3.6deg - 90deg)) translate(calc(var(--size) / 2 - 50%));
}
.radio {
    flex-shrink: 0
}
.range {
    height: 1.5rem;
    width: 100%;
    cursor: pointer;
}
  .range:focus {
    outline: none;
  }
.rating {
    position: relative;
    display: inline-flex
}
  .rating :where(input) {
    cursor: pointer;
    border-radius: 0px
}
.select {
    display: inline-flex;
    cursor: pointer;
    user-select: none;
    appearance: none;
    height: 3rem;
    min-height: 3rem;
    padding-inline-start: 1rem;
    padding-inline-end: 2.5rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    line-height: 2
}

  /* disabled */
  /* &-disabled,
  &[disabled] {
    @apply pointer-events-none;
  } */
  /* multiple */
  .select[multiple] {
    height: auto
}
.stack {
    display: inline-grid;
}
  .stack > * {
    grid-column-start: 1;
    grid-row-start: 1;
    transform: translateY(10%) scale(0.9);
    z-index: 1;
}
  .stack > *:nth-child(2) {
    transform: translateY(5%) scale(0.95);
    z-index: 2;
  }
  .stack > *:nth-child(1) {
    transform: translateY(0) scale(1);
    z-index: 3;
  }
.stats {
  display: inline-grid
}
:where(.stats) {
  grid-auto-flow: column
}
.stat {
  display: inline-grid;
  width: 100%;
  grid-template-columns: repeat(1, 1fr)
}
.stat-figure {
  grid-column-start: 2;
  grid-row: span 3 / span 3;
  grid-row-start: 1;
  place-self: center;
  justify-self: end
}
.stat-title {
  grid-column-start: 1;
  white-space: nowrap
}
.stat-value {
  grid-column-start: 1;
  white-space: nowrap
}
.stat-desc {
  grid-column-start: 1;
  white-space: nowrap
}
.stat-actions {
  grid-column-start: 1;
  white-space: nowrap
}
/* .stats.grid-flow-row {
  @apply auto-rows-fr;
} */
.steps {
  display: inline-grid;
  grid-auto-flow: column;
  overflow: hidden;
  overflow-x: auto;
  counter-reset: step;
  grid-auto-columns: 1fr
}
  .steps .step {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  grid-template-rows: repeat(2, minmax(0, 1fr));
  place-items: center;
  text-align: center
}
.swap {

    position: relative;

    display: inline-grid;

    user-select: none;

    place-content: center
}

.swap > * {

    grid-column-start: 1;

    grid-row-start: 1
}

.swap input {

    appearance: none
}

.swap .swap-on,
.swap .swap-indeterminate,
.swap input:indeterminate ~ .swap-on {

    opacity: 0
}

.swap input:checked ~ .swap-off,
.swap-active .swap-off,
.swap input:indeterminate ~ .swap-off {

    opacity: 0
}

.swap input:checked ~ .swap-on,
.swap-active .swap-on,
.swap input:indeterminate ~ .swap-indeterminate {

    opacity: 1
}
.tabs {
  display: grid;
  align-items: flex-end;
}
.tabs-lifted:has(.tab-content[class^="rounded-"])
    .tab:first-child:not(:is(.tab-active, [aria-selected="true"])), .tabs-lifted:has(.tab-content[class*=" rounded-"])
    .tab:first-child:not(:is(.tab-active, [aria-selected="true"])) {
  border-bottom-color: transparent;
}
.tab {
  position: relative;
  grid-row-start: 1;
  display: inline-flex;
  height: 2rem;
  cursor: pointer;
  user-select: none;
  appearance: none;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 0.875rem;
  line-height: 1.25rem;
  line-height: 2;
  --tab-padding: 1rem;
}
.tab:is(input[type="radio"]) {
  width: auto;
  border-bottom-right-radius: 0px;
  border-bottom-left-radius: 0px;
}
.tab:is(input[type="radio"]):after {
      --tw-content: attr(aria-label);
      content: var(--tw-content);
    }
.tab:not(input):empty {
  cursor: default;
    grid-column-start: span 9999;
}
.tab-content {
  grid-column-start: 1;
  grid-column-end: span 9999;
  grid-row-start: 2;
  margin-top: calc(var(--tab-border) * -1);
  display: none;
  border-color: transparent;
  border-width: var(--tab-border, 0);
}
:checked + .tab-content:nth-child(2),
  :is(.tab-active, [aria-selected="true"]) + .tab-content:nth-child(2) {
  border-start-start-radius: 0px;
}
input.tab:checked + .tab-content,
:is(.tab-active, [aria-selected="true"]) + .tab-content {
  display: block;
}
.table {
    position: relative;
    width: 100%
}
  .table :where(.table-pin-rows thead tr) {
    position: sticky;
    top: 0px;
    z-index: 1;
    --tw-bg-opacity: 1;
    background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)))
}
  .table :where(.table-pin-rows tfoot tr) {
    position: sticky;
    bottom: 0px;
    z-index: 1;
    --tw-bg-opacity: 1;
    background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)))
}
  .table :where(.table-pin-cols tr th) {
    position: sticky;
    left: 0px;
    right: 0px;
    --tw-bg-opacity: 1;
    background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)))
}
  .table-zebra tbody tr:nth-child(even) :where(.table-pin-cols tr th) {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)))
}
.textarea {
    min-height: 3rem;
    flex-shrink: 1;
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    line-height: 2
}
.timeline {
  position: relative;
  display: flex
}
:where(.timeline > li) {
  position: relative;
  display: grid;
  flex-shrink: 0;
  align-items: center;
  grid-template-rows: var(--timeline-row-start, minmax(0, 1fr)) auto var(
      --timeline-row-end,
      minmax(0, 1fr)
    );
  grid-template-columns: var(--timeline-col-start, minmax(0, 1fr)) auto var(
      --timeline-col-end,
      minmax(0, 1fr)
    )
}
.timeline > li > hr {
  width: 100%;
  border-width: 0px
}
:where(.timeline > li > hr):first-child {
  grid-column-start: 1;
  grid-row-start: 2
}
:where(.timeline > li > hr):last-child {
  grid-column-start: 3;
  grid-column-end: none;
  grid-row-start: 2;
  grid-row-end: auto
}
.timeline-start {
  grid-column-start: 1;
  grid-column-end: 4;
  grid-row-start: 1;
  grid-row-end: 2;
  margin: 0.25rem;
  align-self: flex-end;
  justify-self: center
}
.timeline-middle {
  grid-column-start: 2;
  grid-row-start: 2
}
.timeline-end {
  grid-column-start: 1;
  grid-column-end: 4;
  grid-row-start: 3;
  grid-row-end: 4;
  margin: 0.25rem;
  align-self: flex-start;
  justify-self: center
}
.toast {
    position: fixed;
    display: flex;
    min-width: fit-content;
    flex-direction: column;
    white-space: nowrap
}
.toggle {
    flex-shrink: 0
}
