import User from "../models/user.js";
import jwt from "jsonwebtoken";

export async function signup (req,res){
  const {email,password,fullName} = req.body;
  try{
    if(!email || !password || !fullName){
        return res.status(400).json({
            message:"All fields are required",
        });
    }
    if (password.length < 6){
        return res.status(400).json({
            message:"Password must be at least 6 characters long",
        });
    };
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if(!emailRegex.test(email)){
        return res.status(400).json({
            message:"Invalid email",
        });
    };
    const user = await User.findOne({email});
    if(user){
        return res.status(400).json({
            message:"Email already exists",
        });
    }
    const idx = Math.floor(Math.random() * 100) //generate random num from 1 to 100
    const randomAvtar =`https://avatar.iran.liara.run/public/${idx}.png`;

    const newUser = await User.create({
        email,
        password,
        fullName,
        profilePicture:randomAvtar,
    });

    const token = jwt.sign({UserId:newUser._id},process.env.JWT_SECRET,{
        expiresIn:"7d",
    });
    res.cookie("jwt",token,{
        httpOnly:true,
        sameSite:"none",
        secure: process.env.NODE_ENV === "production",
        maxAge:7*24*60*60*1000,
    });

    res.status(201).json({
        message:"User created successfully",
        user:{
            _id:newUser._id,
            email:newUser.email,
            fullName:newUser.fullName,
            profilePicture:newUser.profilePicture,
        }
    });

  }
  catch(error){
    console.log(error);
    res.status(500).json({
        message:"Something went wrong",
    });
    
  }

}

export async function login (req,res){
    res.send('login route');
}

export async function logout (req,res){
    res.send('logout route');
}