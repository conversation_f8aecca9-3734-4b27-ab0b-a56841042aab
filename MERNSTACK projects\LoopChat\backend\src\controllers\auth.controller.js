import { User } from "../models/user";
import jwt from "jsonwebtoken";

export async function signup (req,res){
  const {email,password,fullName} = req.body;
  try{
    if(email || !password || !fullName){
        return res.status(400).json({
            message:"All fields are required",
        });
    }
    if (password.length < 6){
        return res.status(400).json({
            message:"Password must be at least 6 characters long",
        });
    };
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if(!emailRegex.test(email)){
        return res.status(400).json({
            message:"Invalid email",
        });
    };
    const user = await User.findOne({email});
    if(user){
        return res.status(400).json({
            message:"Email already exists",
        });
    }
    const idx = Math.floor(Math.random() * 100) //generate random num from 1 to 100
    const randomAvtar =`https://avatar.iran.liara.run/public/${idx}.png`;

    const newUser = new User.create({
        email,
        password,
        fullName,
        avatar:randomAvtar,
    });

    const token = jwt.sign({id:newUser._id},process.env.JWT_SECRET);

  }
  catch(error){
    console.log(error);
    res.status(500).json({
        message:"Something went wrong",
    });
  }

}

export async function login (req,res){
    res.send('login route');
}

export async function logout (req,res){
    res.send('logout route');
}