import { upsertStreamUser } from "../lib/stream.js";
import User from "../models/user.js";
import jwt from "jsonwebtoken";


// signup middleware
export async function signup(req, res) {
    const { email, password, fullName } = req.body;//geting input from the user for signup
    try {
        if (!email || !password || !fullName) {
            return res.status(400).json({
                message: "All fields are required",
            });//checking the input are filled or not 
        }
        if (password.length < 6) {
            return res.status(400).json({
                message: "Password must be at least 6 characters long",
            });//checking the password length
        };
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({
                message: "Invalid email",
            });//checking the email is valid or not 
        };
        const user = await User.findOne({ email });
        if (user) {
            return res.status(400).json({
                message: "Email already exists",
            });// checking the email is already exists or not
        }
        const idx = Math.floor(Math.random() * 100) //generate random num from 1 to 100
        const randomAvtar = `https://avatar.iran.liara.run/public/${idx}.png`;

        const newUser = await User.create({
            email,
            password,
            fullName,
            profilePicture: randomAvtar,//creating random avatar for the user 
        });

        try {
            await upsertStreamUser({
                id: newUser._id.toString(),
                name: newUser.fullName,
                image: newUser.profilePic || "",
            });
            console.log(`Stream user created ${newUser.fullName}`);
        }
        catch (error) {
            console.log(error);
        }



        const token = jwt.sign({ UserId: newUser._id }, process.env.JWT_SECRET, {
            expiresIn: "7d",
        });
        res.cookie("jwt", token, {
            httpOnly: true,
            sameSite: "none",
            secure: process.env.NODE_ENV === "production",
            maxAge: 7 * 24 * 60 * 60 * 1000,
        });

        res.status(201).json({
            message: "User created successfully",
            user: {
                _id: newUser._id,
                email: newUser.email,
                fullName: newUser.fullName,
                profilePicture: newUser.profilePicture,
            }
        });

    }
    catch (error) {
        console.log(error);
        res.status(500).json({
            message: "Something went wrong",
        });

    }

}
// login middleware
export async function login(req, res) {
    try {
        const { email, password } = req.body;
        if (!email || !password) {
            return res.status(400).json({ message: "All fields are required" });
        }
        const user = await User.findOne({ email });
        if (!user) {
            return res.status(401).json({ message: "Invalid email or password" });
        }
        const isPasswordValid = await user.matchPassword(password);
        if (!isPasswordValid) {
            return res.status(401).json({ message: "Invalid email or password" });
        }

        const token = jwt.sign({ UserId: user._id }, process.env.JWT_SECRET, {
            expiresIn: "7d",
        });
        res.cookie("jwt", token, {
            httpOnly: true,
            sameSite: "none",
            secure: process.env.NODE_ENV === "production",
            maxAge: 7 * 24 * 60 * 60 * 1000,
        });
        res.status(200).json({
            message: "Logged in successfully",
            user: {
                _id: user._id,
                email: user.email,
                fullName: user.fullName,
                profilePicture: user.profilePicture,
            }
        });
    }
    catch (error) {
        console.log(error.message);
        res.status(500).json({ message: "Something went wrong" });
    }
}


//logout middleware
export async function logout(req, res) {
    res.clearCookie('jwt');
    res.status(200).json({ message: "Logged out successfully" });
}

//onboarding 
//6 satday
export async function onboard(req, res) {
    try {
        const userId = req.user._id;
        const { fullName, bio, nativeLanguage, learningLanguage, location } = req.body;
        if (!fullName || !bio || !nativeLanguage || !learningLanguage || !location) {
            return res.status(400).json({
                message: "All fields are required",
                missingfields: [
                    !fullName && "fullName",
                    !bio && "bio",
                    !nativeLanguage && "nativeLanguage",
                    !learningLanguage && "learningLanguage",
                    !location && "location",
                ].filter(Boolean),
            });
        }
        const updatedUser = await User.findByIdAndUpdate(userId, {
            ...req.body,
            isOnboarded: true,
        }, { new: true }).select("-password");

        if (!updatedUser) {
            return res.status(500).json({ message: "Something went wrong" });
        }
        try {
            await upsertStreamUser({
                id: updatedUser._id.toString(),
                name: updatedUser.fullName,
                image: updatedUser.profilePic || "",
            });
            console.log(`Stream user upadted after onboarding for ${updatedUser.fullName}`);
        }
        catch (streamError) {
            console.log(streamError.message);
        }

        res.status(200).json({
            message: "Onboarding completed successfully",
            user: updatedUser
        });
    } catch (error) {
        console.log(error);
        res.status(500).json({ message: "Something went wrong" });
    }
};