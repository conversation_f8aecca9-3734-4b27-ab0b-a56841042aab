import {StreamChat} from 'stream-chat';
import "dotenv/config";

const apiKey = process.env.STEAM_API_KEY;
const apiSecret = process.env.STEAM_API_SECRET;

if (!apiKey|| !apiSecret){
    console.error("Steream API key and secret are missing");
    
}

const streamClient = StreamChat.getInstance(apiKey, apiSecret);

export const upsertStreamUser = async(userData) =>{
    try {
        await streamClient.upsertUser([userData]);
        return userData;
    } catch (error) {
        console.log(error);
    
    };
};

//TODO:do it later 
export const generateStreamToken = async(userId) =>{



};