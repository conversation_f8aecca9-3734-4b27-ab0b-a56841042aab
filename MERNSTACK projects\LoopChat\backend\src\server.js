import express from 'express';
import dotenv from 'dotenv';
dotenv.config();
import cookieParser from 'cookie-parser';
import authRoutes from './routes/auth.route.js';
import { connectDB } from './lib/db.js';

const app = express();
const PORT = process.env.PORT || 3000;

app.use(express.json());
app.use("/api/auth",authRoutes);
app.use(cookieParser());


app.listen(PORT,() =>{

    console.log(`Server is running on http://localhost:${PORT}`);
    connectDB();
});