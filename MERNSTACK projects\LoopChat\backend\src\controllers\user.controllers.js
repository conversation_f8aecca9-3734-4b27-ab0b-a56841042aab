import User from "../models/user.js";
import FriendRequest from "../models/FriendRequest.js"
export async function getRecommendedUsers(req, res) {
    try {
        const cureentUserId = req.user.id;
        const cureentUser = req.user;

        const recommendedUsers = await User.find({
            $and: [
                { _id: { $ne: cureentUserId } },
                { $id: { $nin: cureentUser.friends } },
                { isOnboarded: true },
            ]
        });
        res.status(200).json(recommendedUsers);
    }
    catch (error) {
        console.error(error.message);
        res.status(500).json({ message: "Internal server error" });
    }

};

export async function getMyFriends(req, res) {
    try {
        const user = await User.findById(req.user.id).select("friends").populate("friends", "fullName profilePicture nativeLanguage learningLanguage location");
        res.status(200).json(user.friends);
    }
    catch (error) {
        console.error(error.message);
        res.status(500).json({ message: "Internal server error" });
    }
};

export async function sendFriendRequest(req, res) {
    try {
        const myId = req.user.id;
        const { id: recipientId } = req.params;
        //checking if a user send a requst to him/herself
        if (myId === recipientId) {
            return res.status(400).json({ message: "You can't send a requst to your self bro" });
        };
        //finding the user
        const recipient = await User.findById(recipientId);
        if (!recipient) {
            return res.status(404).json({ message: "User not found" });
        };
        //check if the user is already friends or not 
        if (recipient.friends.includes(myId)) {
            return res.status(400).json({ message: "You are already friends with this user" });
        };

        //check if the req is exist or not 
        const existingRequest = await FriendRequest.findOne({
            $or: [
                {
                    sender: myId,
                    recipient: recipientId,
                },
                {
                    sender: recipientId,
                    recipient: myId,
                }

            ],
        });
        if (existingRequest) {
            return res.status(400).json({ message: "a friend request already exist for this user" });
        };

        const friendRequest = await FriendRequest.create({
            sender: myId,
            recipient: recipientId,
        });
        res.status(201).json(friendRequest);


    } catch (error) {
        console.error(error.message);
        res.status(500).json({ message: "Internal server error" });
    }
};

export async function acceptFriendRequest(req, res) {
    try {
        const { id: requestId } = req.params;
        const friendRequest = await FriendRequest.findById(requestId);
        if (!friendRequest) {
            return res.status(404).json({ message: "Friend request not found" });

        };
        //Verify the current user is recipient 
        if (friendRequest.recipient.toString() != req.user.id) {
            return res.status(403).json({ message: "You are not authorized to accept this request" });

        }
        friendRequest.status = "accepted";
        await friendRequest.save();

        //add each user to other's friends list
        //$addToSet: adds element to an array only if the element is not already exist 
        await User.findByIdAndUpdate(friendRequest.sender, {
            $addToSet: { friends: friendRequest.recipient },
        });

        await User.findByIdAndUpdate(friendRequest.recipient, {
            $addToSet: { friends: friendRequest.sender },
        });
        res.status(200).json({ message: "Friend request accepted successfully" });

    } catch (error) {
        console.error(error.message);
        res.status(500).json({ message: "Internal server error" });
    }
};


export async function getFriendRequests(req, res) {
    try {
        const incomingReqs = await FriendRequest.find({
            recipient: req.user.id,
            status: "pending",
        }).populate("sender", "fullName profilePicture nativeLanguage learningLanguage location");

        const acceptReqs = await FriendRequest.find({
            recipient: req.user.id,
            status: "pending",
        }).populate("recipient", "fullName profilePicture");

        res.status(200).json({ incomingReqs, acceptReqs });
    } catch (error) {
        console.error(error.message);
        res.status(500).json({ message: "Internal server error" });
    }
};

export async function getOutgoingFriendRequests(req, res) {
    try{
        const outgoingRequest = await FriendRequest.find({
            sender: req.user.id,
            status: "pending",
        }).populate("recipient", "fullName profilePicture nativeLanguage learningLanguage");

        res.status(200).json(outgoingRequest);
    }catch (error){
        console.error(error.message);
        res.status(500).json({ message: "Internal server error" });
    }
};