import jwt from "jsonwebtoken";
import User from "../models/user.js";


export const protectRoute = async(req,res, next)=>{
    try {
        const token = req.cokkies.jwt;
        if(!token){
            return res.status(401).json({message:"Unauthorized"});
            //if token is not present then return unauthorized
        }
        const decoded = jwt.verify(token,process.env.JWT_SECRET);// it will decode the token and return the payload
        if(!decoded){
            return res.status(401).json({message:"Unauthorized - invalid token"});
        }
        const user = await User.findById(decoded.UserId).select("-password"); // it will find the user from the database
        if(!user){
            return res.status(401).json({message:"Unauthorized - user not found"});
        
        }
        req.user = user;
        next();
    }
    catch (error) {
        console.log(error);
        res.status(500).json({message:"Inernal server error"});
    
    }

};