/*
	MIT License http://www.opensource.org/licenses/mit-license.php
	Author <PERSON> @sokra
*/

"use strict";

module.exports = {
	/**
	 * @type {Record<string, string>}
	 */
	versions: {},
	// eslint-disable-next-line jsdoc/no-restricted-syntax
	/**
	 * @param {Function} fn function
	 */
	nextTick(fn) {
		// eslint-disable-next-line prefer-rest-params
		const args = Array.prototype.slice.call(arguments, 1);
		Promise.resolve().then(() => {
			// eslint-disable-next-line prefer-spread
			fn.apply(null, args);
		});
	},
};
