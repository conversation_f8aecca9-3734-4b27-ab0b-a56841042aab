{"name": "backend", "version": "1.0.0", "description": "", "main": "src/server.js", "type": "module", "scripts": {"start": "npm run dev", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "stream-chat": "^8.60.0"}, "devDependencies": {"nodemon": "^3.1.9"}}