import express from "express";
import { protectRoute } from "../middlewares/auth.middleware";
import {
    getMyFriends,
    getRecommendedUsers,
    sendFriendRequest,
    acceptFriendRequest,
    getFriendRequests,
    getOutgoingFriendRequests,
  
} from "../controllers/user.controllers.js";

const router = express.Router();
router.use(protectRoute);

router.get("/", getRecommendedUsers);
router.get("/friends", getMyFriends);

router.post("/friend-request/:id", sendFriendRequest);
router.put("/friend-request/:id/accept", acceptFriendRequest);



router.get("/friend-requests", getFriendRequests);
router.get("/outing-friend-requests", getOutgoingFriendRequests);
export default router;