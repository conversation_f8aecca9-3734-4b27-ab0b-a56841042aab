![culori](./docs/img/culori.svg)

<a href="https://www.npmjs.org/package/culori"><img src="https://img.shields.io/npm/v/culori.svg?style=flat-square&labelColor=d84f4c&color=black" alt="npm version"></a> <a href="https://bundlephobia.com/result?p=culori"><img src="https://img.shields.io/bundlephobia/minzip/culori?style=flat-square&labelColor=d84f4c&color=black" alt="bundle size"></a>

Culori is a comprehensive color library for JavaScript that works across many color spaces to offer conversion, interpolation, color difference formulas, blending functions, and more. It provides up-to-date support for the color spaces defined in [CSS Color Module Level 4](https://drafts.csswg.org/css-color/) specification.

```bash
npm install culori
```

The full documentation is published on [culorijs.org](https://culorijs.org). Some quick entry points:

-   [Getting started](https://culorijs.org/getting-started)
-   [API References](https://culorijs.org/api/)
-   [Supported color spaces](https://culorijs.org/color-spaces/)

## Contributing

Contributions of any kind (feedback, ideas, bug fixes) are welcome. Please open a GitHub issue before starting work on anything that's not straightforward.
