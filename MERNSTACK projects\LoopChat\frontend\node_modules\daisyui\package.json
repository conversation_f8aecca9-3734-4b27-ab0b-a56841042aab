{"name": "daisyui", "version": "4.12.24", "description": "daisyUI - Tailwind CSS Components", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "homepage": "https://daisyui.com", "repository": {"type": "git", "url": "git+https://github.com/saadeghi/daisyui.git"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/daisyui"}, "bugs": {"url": "https://github.com/saadeghi/daisyui/issues"}, "keywords": ["design-system", "tailwindcss", "components", "ui-library", "component", "framework", "tailwind", "daisyui", "theming", "postcss", "design", "css", "ui"], "main": "src/index.js", "typings": "src/index.d.ts", "types": "src/index.d.ts", "files": ["src/lib/**/*.js", "dist/*.js", "dist/{themes,styled,unstyled,full}.css", "src/index.js", "src/theming/*.js", "src/theming/*.d.ts", "src/index.d.ts"], "engines": {"node": ">=16.9.0"}, "browserslist": ["> 7%"], "publishConfig": {"access": "public", "branches": ["master"]}, "scripts": {"init": "npm install && npm run build && cd src/docs && npm install && npm run get-json --silent && cd src/experiments/playground && npm install", "format": "biome format --write .", "lint": "biome lint . --write", "build": "node src/build", "build:skipfullcss": "node src/build --skipfullcss", "dev": "cd src/docs && npm run dev", "playground": "cd src/experiments/playground && npm run dev", "release": "node src/release", "publish:alpha": "npm publish --tag=alpha", "alpha": "npm run release -- --alpha && npm publish --tag=alpha"}, "devDependencies": {"@biomejs/biome": "^1.9.1", "autoprefixer": "10.4.19", "commit-and-tag-version": "12.4.1", "postcss-cli": "11.0.0", "postcss-import": "16.1.0", "prejss-cli": "0.3.3", "tailwindcss": "3.4.4"}, "dependencies": {"css-selector-tokenizer": "^0.8", "culori": "^3", "picocolors": "^1", "postcss-js": "^4"}}