module.exports = {".glass,\n  .glass.btn-active":{"border":"none","backdropFilter":"blur(var(--glass-blur, 40px))","backgroundColor":"transparent","backgroundImage":"linear-gradient(\n        135deg,\n        rgb(255 255 255 / var(--glass-opacity, 30%)) 0%,\n        rgb(0 0 0 / 0%) 100%\n      ),\n      linear-gradient(\n        var(--glass-reflex-degree, 100deg),\n        rgb(255 255 255 / var(--glass-reflex-opacity, 10%)) 25%,\n        rgb(0 0 0 / 0%) 25%\n      )","boxShadow":"0 0 0 1px rgb(255 255 255 / var(--glass-border-opacity, 10%)) inset,\n      0 0 0 2px rgb(0 0 0 / 5%)","textShadow":"0 1px rgb(0 0 0 / var(--glass-text-shadow-opacity, 5%))"},"@media (hover: hover)":{".glass.btn-active":{"border":"none","backdropFilter":"blur(var(--glass-blur, 40px))","backgroundColor":"transparent","backgroundImage":"linear-gradient(\n          135deg,\n          rgb(255 255 255 / var(--glass-opacity, 30%)) 0%,\n          rgb(0 0 0 / 0%) 100%\n        ),\n        linear-gradient(\n          var(--glass-reflex-degree, 100deg),\n          rgb(255 255 255 / var(--glass-reflex-opacity, 10%)) 25%,\n          rgb(0 0 0 / 0%) 25%\n        )","boxShadow":"0 0 0 1px rgb(255 255 255 / var(--glass-border-opacity, 10%)) inset,\n        0 0 0 2px rgb(0 0 0 / 5%)","textShadow":"0 1px rgb(0 0 0 / var(--glass-text-shadow-opacity, 5%))"}},".no-animation":{"-BtnFocusScale":"1","-AnimationBtn":"0","-AnimationInput":"0"},".tab-border-none":{"-TabBorder":"0px"},".tab-border":{"-TabBorder":"1px"},".tab-border-2":{"-TabBorder":"2px"},".tab-border-3":{"-TabBorder":"3px"},".tab-rounded-none":{"-TabRadius":"0"},".tab-rounded-lg":{"-TabRadius":"0.5rem"}};